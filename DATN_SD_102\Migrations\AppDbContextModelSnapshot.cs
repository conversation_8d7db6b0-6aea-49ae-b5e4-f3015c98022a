﻿// <auto-generated />
using System;
using F4_API.DATA;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace F4_API.Migrations
{
    [DbContext(typeof(AppDbContext))]
    partial class AppDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.17")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("F4_API.Models.ChiTietNhapHang", b =>
                {
                    b.Property<Guid>("ChiTietNhapHangId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("GiaTien")
                        .HasColumnType("float");

                    b.Property<Guid?>("LinhKienCTId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("NhapHangId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("SoLuong")
                        .HasColumnType("int");

                    b.HasKey("ChiTietNhapHangId");

                    b.HasIndex("LinhKienCTId");

                    b.HasIndex("NhapHangId");

                    b.ToTable("ChiTietNhapHangs");
                });

            modelBuilder.Entity("F4_API.Models.ChucVu", b =>
                {
                    b.Property<Guid>("ChucVuId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("MoTaChucVu")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TenChucVu")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool?>("TrangThai")
                        .HasColumnType("bit");

                    b.HasKey("ChucVuId");

                    b.ToTable("ChucVus");

                    b.HasData(
                        new
                        {
                            ChucVuId = new Guid("11111111-1111-1111-1111-111111111111"),
                            MoTaChucVu = "Quản trị hệ thống",
                            TenChucVu = "Admin",
                            TrangThai = true
                        },
                        new
                        {
                            ChucVuId = new Guid("*************-2222-2222-************"),
                            MoTaChucVu = "Nhân viên bán hàng",
                            TenChucVu = "NhanVien",
                            TrangThai = true
                        });
                });

            modelBuilder.Entity("F4_API.Models.Combo", b =>
                {
                    b.Property<Guid>("ComboId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("GiaTien")
                        .HasColumnType("float");

                    b.Property<string>("MoTa")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("NgayTao")
                        .HasColumnType("datetime2");

                    b.Property<string>("Ten")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TrangThai")
                        .HasColumnType("bit");

                    b.HasKey("ComboId");

                    b.ToTable("Combos");
                });

            modelBuilder.Entity("F4_API.Models.ComboChiTiet", b =>
                {
                    b.Property<Guid>("ComboChiTietId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ComboId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("LinhKienId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("SoLuong")
                        .HasColumnType("int");

                    b.HasKey("ComboChiTietId");

                    b.HasIndex("ComboId");

                    b.HasIndex("LinhKienId");

                    b.ToTable("ComboChiTiets");
                });

            modelBuilder.Entity("F4_API.Models.DanhMuc", b =>
                {
                    b.Property<Guid>("DanhMucId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("MoTa")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TenDanhMuc")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("TrangThai")
                        .HasColumnType("bit");

                    b.HasKey("DanhMucId");

                    b.ToTable("DanhMucs");

                    b.HasData(
                        new
                        {
                            DanhMucId = new Guid("00000000-0000-0000-0000-000000000001"),
                            MoTa = "Trung tâm xử lý dữ liệu chính của máy tính.",
                            TenDanhMuc = "CPU (Vi xử lý)",
                            TrangThai = true
                        },
                        new
                        {
                            DanhMucId = new Guid("00000000-0000-0000-0000-000000000002"),
                            MoTa = "Kết nối các linh kiện lại với nhau, cung cấp nguồn và tín hiệu.",
                            TenDanhMuc = "Mainboard (Bo mạch chủ)",
                            TrangThai = true
                        },
                        new
                        {
                            DanhMucId = new Guid("00000000-0000-0000-0000-000000000003"),
                            MoTa = "Lưu trữ dữ liệu tạm thời khi máy hoạt động, càng nhiều RAM thì xử lý đa nhiệm càng tốt.",
                            TenDanhMuc = "RAM (Bộ nhớ tạm)",
                            TrangThai = true
                        },
                        new
                        {
                            DanhMucId = new Guid("00000000-0000-0000-0000-000000000004"),
                            MoTa = "Lưu trữ hệ điều hành, phần mềm, dữ liệu người dùng.",
                            TenDanhMuc = "Ổ cứng (SSD/HDD)",
                            TrangThai = true
                        },
                        new
                        {
                            DanhMucId = new Guid("00000000-0000-0000-0000-000000000005"),
                            MoTa = "Cung cấp điện năng cho toàn bộ hệ thống.",
                            TenDanhMuc = "Nguồn (PSU)",
                            TrangThai = true
                        },
                        new
                        {
                            DanhMucId = new Guid("00000000-0000-0000-0000-000000000006"),
                            MoTa = "Bảo vệ linh kiện, hỗ trợ tản nhiệt, định hình hệ thống.",
                            TenDanhMuc = "Case (Vỏ máy tính)",
                            TrangThai = true
                        },
                        new
                        {
                            DanhMucId = new Guid("00000000-0000-0000-0000-000000000007"),
                            MoTa = "Dùng cho xử lý đồ họa, gaming, thiết kế, dựng phim. Một số CPU đã tích hợp GPU sẵn.",
                            TenDanhMuc = "Card đồ họa (GPU)",
                            TrangThai = true
                        },
                        new
                        {
                            DanhMucId = new Guid("00000000-0000-0000-0000-000000000008"),
                            MoTa = "Có thể là tản nhiệt khí hoặc nước, dùng cho CPU hoặc cả hệ thống.",
                            TenDanhMuc = "Tản nhiệt (Cooling)",
                            TrangThai = true
                        });
                });

            modelBuilder.Entity("F4_API.Models.DanhMuc_LinhKien_ThuocTinh", b =>
                {
                    b.Property<Guid>("ThuocTinh")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("DanhMucId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("DonVi")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TenThuocTinh")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("TrangThai")
                        .HasColumnType("bit");

                    b.HasKey("ThuocTinh");

                    b.HasIndex("DanhMucId");

                    b.ToTable("DanhMuc_LinhKien_ThuocTinhs");

                    b.HasData(
                        new
                        {
                            ThuocTinh = new Guid("11111111-0000-0000-0000-000000000001"),
                            DanhMucId = new Guid("00000000-0000-0000-0000-000000000001"),
                            DonVi = "Nhân",
                            TenThuocTinh = "Số nhân",
                            TrangThai = true
                        },
                        new
                        {
                            ThuocTinh = new Guid("11111111-0000-0000-0000-000000000002"),
                            DanhMucId = new Guid("00000000-0000-0000-0000-000000000001"),
                            DonVi = "Luồng",
                            TenThuocTinh = "Số luồng",
                            TrangThai = true
                        },
                        new
                        {
                            ThuocTinh = new Guid("11111111-0000-0000-0000-000000000003"),
                            DanhMucId = new Guid("00000000-0000-0000-0000-000000000001"),
                            DonVi = "GHz",
                            TenThuocTinh = "Xung nhịp",
                            TrangThai = true
                        },
                        new
                        {
                            ThuocTinh = new Guid("*************-0000-0000-000000000001"),
                            DanhMucId = new Guid("00000000-0000-0000-0000-000000000002"),
                            TenThuocTinh = "Socket",
                            TrangThai = true
                        },
                        new
                        {
                            ThuocTinh = new Guid("*************-0000-0000-000000000002"),
                            DanhMucId = new Guid("00000000-0000-0000-0000-000000000002"),
                            TenThuocTinh = "Chipset",
                            TrangThai = true
                        },
                        new
                        {
                            ThuocTinh = new Guid("*************-0000-0000-000000000001"),
                            DanhMucId = new Guid("00000000-0000-0000-0000-000000000003"),
                            DonVi = "GB",
                            TenThuocTinh = "Dung lượng",
                            TrangThai = true
                        },
                        new
                        {
                            ThuocTinh = new Guid("*************-0000-0000-000000000002"),
                            DanhMucId = new Guid("00000000-0000-0000-0000-000000000003"),
                            DonVi = "MHz",
                            TenThuocTinh = "Bus",
                            TrangThai = true
                        },
                        new
                        {
                            ThuocTinh = new Guid("*************-0000-0000-000000000001"),
                            DanhMucId = new Guid("00000000-0000-0000-0000-000000000004"),
                            TenThuocTinh = "Loại",
                            TrangThai = true
                        },
                        new
                        {
                            ThuocTinh = new Guid("*************-0000-0000-000000000002"),
                            DanhMucId = new Guid("00000000-0000-0000-0000-000000000004"),
                            DonVi = "GB",
                            TenThuocTinh = "Dung lượng",
                            TrangThai = true
                        },
                        new
                        {
                            ThuocTinh = new Guid("*************-0000-0000-000000000001"),
                            DanhMucId = new Guid("00000000-0000-0000-0000-000000000005"),
                            DonVi = "W",
                            TenThuocTinh = "Công suất",
                            TrangThai = true
                        },
                        new
                        {
                            ThuocTinh = new Guid("*************-0000-0000-000000000001"),
                            DanhMucId = new Guid("00000000-0000-0000-0000-000000000006"),
                            TenThuocTinh = "Loại vỏ",
                            TrangThai = true
                        },
                        new
                        {
                            ThuocTinh = new Guid("*************-0000-0000-000000000001"),
                            DanhMucId = new Guid("00000000-0000-0000-0000-000000000007"),
                            DonVi = "GB",
                            TenThuocTinh = "Dung lượng VRAM",
                            TrangThai = true
                        },
                        new
                        {
                            ThuocTinh = new Guid("*************-0000-0000-000000000001"),
                            DanhMucId = new Guid("00000000-0000-0000-0000-000000000008"),
                            TenThuocTinh = "Loại tản",
                            TrangThai = true
                        });
                });

            modelBuilder.Entity("F4_API.Models.DiaChiKhachHang", b =>
                {
                    b.Property<Guid>("DiaChiKhachHangId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("KhachHangId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("MoTa")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhuongXa")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QuanHuyen")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TenDiaChi")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ThanhPho")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TrangThai")
                        .HasColumnType("bit");

                    b.HasKey("DiaChiKhachHangId");

                    b.HasIndex("KhachHangId");

                    b.ToTable("DiaChiKhachHangs");
                });

            modelBuilder.Entity("F4_API.Models.GiamGia", b =>
                {
                    b.Property<Guid>("GiamGiaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("NgayBatDau")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("NgayKetThuc")
                        .HasColumnType("datetime2");

                    b.Property<double>("PhanTramGiam")
                        .HasColumnType("float");

                    b.Property<string>("SanPhamKhuyenMai")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TenGiamGia")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("TrangThai")
                        .HasColumnType("bit");

                    b.HasKey("GiamGiaId");

                    b.ToTable("GiamGias");
                });

            modelBuilder.Entity("F4_API.Models.GioHang", b =>
                {
                    b.Property<Guid>("GioHangId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("KhachHangId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("datetime2");

                    b.HasKey("GioHangId");

                    b.HasIndex("KhachHangId");

                    b.ToTable("GioHangs");
                });

            modelBuilder.Entity("F4_API.Models.GioHangCT", b =>
                {
                    b.Property<Guid>("GioHangCTId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("DonGia")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid>("GioHangId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("LinhKienLkId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("LkId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("datetime2");

                    b.Property<int>("SoLuong")
                        .HasColumnType("int");

                    b.Property<bool>("TrangThai")
                        .HasColumnType("bit");

                    b.HasKey("GioHangCTId");

                    b.HasIndex("GioHangId");

                    b.HasIndex("LinhKienLkId");

                    b.ToTable("GioHangCTs");
                });

            modelBuilder.Entity("F4_API.Models.HinhAnh", b =>
                {
                    b.Property<Guid>("HinhAnhId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("DuongDan")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("LinhKienCtId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("MoTa")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TenAnh")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TrangThai")
                        .HasColumnType("bit");

                    b.HasKey("HinhAnhId");

                    b.HasIndex("LinhKienCtId");

                    b.ToTable("HinhAnhs");
                });

            modelBuilder.Entity("F4_API.Models.HinhThucThanhToan", b =>
                {
                    b.Property<Guid>("HinhThucThanhToanId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("MoTa")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TenHinhThuc")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("HinhThucThanhToanId");

                    b.ToTable("HinhThucThanhToans");
                });

            modelBuilder.Entity("F4_API.Models.HoaDon", b =>
                {
                    b.Property<Guid>("HoaDonId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("EmailKh")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("GhiChu")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("HinhThucThanhToanId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("KhachHangId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("NgayNhanHang")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("datetime2");

                    b.Property<string>("SoDienThoaiKh")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("TaiKhoanId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("TenKhachHang")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TienShip")
                        .HasColumnType("decimal(18,2)");

                    b.Property<double>("TongTienSauKhiGiam")
                        .HasColumnType("float");

                    b.Property<string>("TrangThai")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("VoucherId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("HoaDonId");

                    b.HasIndex("HinhThucThanhToanId");

                    b.HasIndex("KhachHangId");

                    b.HasIndex("TaiKhoanId");

                    b.HasIndex("VoucherId");

                    b.ToTable("HoaDons");
                });

            modelBuilder.Entity("F4_API.Models.HoaDonCT", b =>
                {
                    b.Property<Guid>("HoaDonChiTietId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ComboId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("Gia")
                        .HasColumnType("float");

                    b.Property<Guid?>("HoaDonId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("LinhKienCTLkctId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("LkctId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("SoLuongSanPham")
                        .HasColumnType("int");

                    b.HasKey("HoaDonChiTietId");

                    b.HasIndex("ComboId");

                    b.HasIndex("HoaDonId");

                    b.HasIndex("LinhKienCTLkctId");

                    b.ToTable("HoaDonCTs");
                });

            modelBuilder.Entity("F4_API.Models.KhachHang", b =>
                {
                    b.Property<Guid>("KhachHangId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("GioiTinh")
                        .HasColumnType("bit");

                    b.Property<DateTime>("NgayCapNhatCuoiCung")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("datetime2");

                    b.Property<string>("Sdt")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("TaiKhoanId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("TenKhachHang")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("TrangThai")
                        .HasColumnType("bit");

                    b.HasKey("KhachHangId");

                    b.HasIndex("TaiKhoanId");

                    b.ToTable("KhachHangs");
                });

            modelBuilder.Entity("F4_API.Models.LKDotGiamGia", b =>
                {
                    b.Property<Guid>("LKDotGiamGiaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("GiamGiaId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("LinhKienLkId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("LkId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("LKDotGiamGiaId");

                    b.HasIndex("GiamGiaId");

                    b.HasIndex("LinhKienLkId");

                    b.ToTable("LKDotGiamGias");
                });

            modelBuilder.Entity("F4_API.Models.LinhKien", b =>
                {
                    b.Property<Guid>("LkId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("DanhMucId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("Gia")
                        .HasColumnType("float");

                    b.Property<string>("MoTa")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TenLinhKien")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool?>("TrangThai")
                        .HasColumnType("bit");

                    b.HasKey("LkId");

                    b.HasIndex("DanhMucId");

                    b.ToTable("LinhKiens");
                });

            modelBuilder.Entity("F4_API.Models.LinhKienCT", b =>
                {
                    b.Property<Guid>("LkctId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("GiaTri")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("HinhAnhId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("LinhKienLkId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("LkId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("MoTa")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("SoLuongTonKho")
                        .HasColumnType("int");

                    b.Property<Guid?>("ThuocTinhId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ThuongHieuId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool?>("TrangThai")
                        .HasColumnType("bit");

                    b.HasKey("LkctId");

                    b.HasIndex("LinhKienLkId");

                    b.HasIndex("ThuocTinhId");

                    b.HasIndex("ThuongHieuId");

                    b.ToTable("LinhKienCTs");
                });

            modelBuilder.Entity("F4_API.Models.NhanVien", b =>
                {
                    b.Property<Guid>("NhanVienId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ChucVuId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("DiaChi")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("GioiTinh")
                        .HasColumnType("bit");

                    b.Property<string>("HoVaTen")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("NgayCapNhatCuoiCung")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("NgaySinh")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("NgayTao")
                        .HasColumnType("datetime2");

                    b.Property<string>("Sdt")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("TaiKhoanId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("TrangThai")
                        .HasColumnType("bit");

                    b.HasKey("NhanVienId");

                    b.HasIndex("ChucVuId");

                    b.HasIndex("TaiKhoanId");

                    b.ToTable("NhanViens");

                    b.HasData(
                        new
                        {
                            NhanVienId = new Guid("*************-8888-8888-************"),
                            ChucVuId = new Guid("11111111-1111-1111-1111-111111111111"),
                            DiaChi = "Tòa nhà FPT Polytechnic, phố Trịnh Văn Bô, phường Phương Canh, quận Nam Từ Liêm, TP. Hà Nội",
                            Email = "<EMAIL>",
                            GioiTinh = false,
                            HoVaTen = "Nguyễn Văn Quản Trị",
                            NgayCapNhatCuoiCung = new DateTime(2025, 7, 2, 22, 43, 15, 589, DateTimeKind.Local).AddTicks(8038),
                            NgaySinh = new DateTime(1995, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NgayTao = new DateTime(2025, 7, 2, 22, 43, 15, 589, DateTimeKind.Local).AddTicks(8037),
                            Sdt = "0987654321",
                            TaiKhoanId = new Guid("*************-9999-9999-************"),
                            TrangThai = true
                        });
                });

            modelBuilder.Entity("F4_API.Models.NhapHang", b =>
                {
                    b.Property<Guid>("NhapHangId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("NgayNhap")
                        .HasColumnType("datetime2");

                    b.Property<string>("NhaCungCap")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("NhanVienId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("NhapHangId");

                    b.HasIndex("NhanVienId");

                    b.ToTable("NhapHangs");
                });

            modelBuilder.Entity("F4_API.Models.TaiKhoan", b =>
                {
                    b.Property<Guid>("TaiKhoanId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("NgayTaoTaiKhoan")
                        .HasColumnType("datetime2");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("TaiKhoanId");

                    b.ToTable("TaiKhoans");

                    b.HasData(
                        new
                        {
                            TaiKhoanId = new Guid("*************-9999-9999-************"),
                            NgayTaoTaiKhoan = new DateTime(2025, 7, 2, 22, 43, 15, 589, DateTimeKind.Local).AddTicks(8000),
                            Password = "admin123",
                            Username = "admin"
                        });
                });

            modelBuilder.Entity("F4_API.Models.ThuongHieu", b =>
                {
                    b.Property<Guid>("ThuongHieuId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("DiaChi")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MoTa")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Sdt")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TenThuongHieu")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("TrangThai")
                        .HasColumnType("bit");

                    b.HasKey("ThuongHieuId");

                    b.ToTable("ThuongHieus");
                });

            modelBuilder.Entity("F4_API.Models.Voucher", b =>
                {
                    b.Property<Guid>("VoucherId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("IdTaiKhoan")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("NgayBatDau")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("NgayKetThuc")
                        .HasColumnType("datetime2");

                    b.Property<float>("PhanTram")
                        .HasColumnType("real");

                    b.Property<int>("SoLuong")
                        .HasColumnType("int");

                    b.Property<Guid?>("TaiKhoanId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("TenVoucher")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("TrangThai")
                        .HasColumnType("bit");

                    b.HasKey("VoucherId");

                    b.HasIndex("TaiKhoanId");

                    b.ToTable("Vouchers");
                });

            modelBuilder.Entity("F4_API.Models.ChiTietNhapHang", b =>
                {
                    b.HasOne("F4_API.Models.LinhKienCT", "LinhKienCT")
                        .WithMany()
                        .HasForeignKey("LinhKienCTId");

                    b.HasOne("F4_API.Models.NhapHang", "NhapHang")
                        .WithMany("ChiTiets")
                        .HasForeignKey("NhapHangId");

                    b.Navigation("LinhKienCT");

                    b.Navigation("NhapHang");
                });

            modelBuilder.Entity("F4_API.Models.ComboChiTiet", b =>
                {
                    b.HasOne("F4_API.Models.Combo", "Combo")
                        .WithMany("ChiTiets")
                        .HasForeignKey("ComboId");

                    b.HasOne("F4_API.Models.LinhKienCT", "LinhKienCT")
                        .WithMany()
                        .HasForeignKey("LinhKienId");

                    b.Navigation("Combo");

                    b.Navigation("LinhKienCT");
                });

            modelBuilder.Entity("F4_API.Models.DanhMuc_LinhKien_ThuocTinh", b =>
                {
                    b.HasOne("F4_API.Models.DanhMuc", "DanhMuc")
                        .WithMany("ThuocTinhs")
                        .HasForeignKey("DanhMucId");

                    b.Navigation("DanhMuc");
                });

            modelBuilder.Entity("F4_API.Models.DiaChiKhachHang", b =>
                {
                    b.HasOne("F4_API.Models.KhachHang", "KhachHang")
                        .WithMany("DiaChiKhachHangs")
                        .HasForeignKey("KhachHangId");

                    b.Navigation("KhachHang");
                });

            modelBuilder.Entity("F4_API.Models.GioHang", b =>
                {
                    b.HasOne("F4_API.Models.KhachHang", "KhachHang")
                        .WithMany("GioHangs")
                        .HasForeignKey("KhachHangId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("KhachHang");
                });

            modelBuilder.Entity("F4_API.Models.GioHangCT", b =>
                {
                    b.HasOne("F4_API.Models.GioHang", "GioHang")
                        .WithMany("ChiTiets")
                        .HasForeignKey("GioHangId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("F4_API.Models.LinhKien", "LinhKien")
                        .WithMany("GioHangCTs")
                        .HasForeignKey("LinhKienLkId");

                    b.Navigation("GioHang");

                    b.Navigation("LinhKien");
                });

            modelBuilder.Entity("F4_API.Models.HinhAnh", b =>
                {
                    b.HasOne("F4_API.Models.LinhKienCT", "LinhKien")
                        .WithMany("HinhAnhs")
                        .HasForeignKey("LinhKienCtId");

                    b.Navigation("LinhKien");
                });

            modelBuilder.Entity("F4_API.Models.HoaDon", b =>
                {
                    b.HasOne("F4_API.Models.HinhThucThanhToan", "HinhThucThanhToan")
                        .WithMany("HoaDons")
                        .HasForeignKey("HinhThucThanhToanId");

                    b.HasOne("F4_API.Models.KhachHang", "KhachHang")
                        .WithMany("HoaDons")
                        .HasForeignKey("KhachHangId");

                    b.HasOne("F4_API.Models.TaiKhoan", "TaiKhoan")
                        .WithMany("HoaDons")
                        .HasForeignKey("TaiKhoanId");

                    b.HasOne("F4_API.Models.Voucher", "Voucher")
                        .WithMany("HoaDons")
                        .HasForeignKey("VoucherId");

                    b.Navigation("HinhThucThanhToan");

                    b.Navigation("KhachHang");

                    b.Navigation("TaiKhoan");

                    b.Navigation("Voucher");
                });

            modelBuilder.Entity("F4_API.Models.HoaDonCT", b =>
                {
                    b.HasOne("F4_API.Models.Combo", "Combo")
                        .WithMany("HoaDonCTs")
                        .HasForeignKey("ComboId");

                    b.HasOne("F4_API.Models.HoaDon", "HoaDon")
                        .WithMany("ChiTiets")
                        .HasForeignKey("HoaDonId");

                    b.HasOne("F4_API.Models.LinhKienCT", "LinhKienCT")
                        .WithMany()
                        .HasForeignKey("LinhKienCTLkctId");

                    b.Navigation("Combo");

                    b.Navigation("HoaDon");

                    b.Navigation("LinhKienCT");
                });

            modelBuilder.Entity("F4_API.Models.KhachHang", b =>
                {
                    b.HasOne("F4_API.Models.TaiKhoan", "TaiKhoan")
                        .WithMany("KhachHangs")
                        .HasForeignKey("TaiKhoanId");

                    b.Navigation("TaiKhoan");
                });

            modelBuilder.Entity("F4_API.Models.LKDotGiamGia", b =>
                {
                    b.HasOne("F4_API.Models.GiamGia", "GiamGia")
                        .WithMany("DotGiamGias")
                        .HasForeignKey("GiamGiaId");

                    b.HasOne("F4_API.Models.LinhKien", "LinhKien")
                        .WithMany("DotGiamGias")
                        .HasForeignKey("LinhKienLkId");

                    b.Navigation("GiamGia");

                    b.Navigation("LinhKien");
                });

            modelBuilder.Entity("F4_API.Models.LinhKien", b =>
                {
                    b.HasOne("F4_API.Models.DanhMuc", "DanhMuc")
                        .WithMany("LinhKiens")
                        .HasForeignKey("DanhMucId");

                    b.Navigation("DanhMuc");
                });

            modelBuilder.Entity("F4_API.Models.LinhKienCT", b =>
                {
                    b.HasOne("F4_API.Models.LinhKien", null)
                        .WithMany("ChiTiets")
                        .HasForeignKey("LinhKienLkId");

                    b.HasOne("F4_API.Models.DanhMuc_LinhKien_ThuocTinh", "ThuocTinh")
                        .WithMany("LinhKienCTs")
                        .HasForeignKey("ThuocTinhId");

                    b.HasOne("F4_API.Models.ThuongHieu", "ThuongHieu")
                        .WithMany("LinhKienCTs")
                        .HasForeignKey("ThuongHieuId");

                    b.Navigation("ThuocTinh");

                    b.Navigation("ThuongHieu");
                });

            modelBuilder.Entity("F4_API.Models.NhanVien", b =>
                {
                    b.HasOne("F4_API.Models.ChucVu", "ChucVu")
                        .WithMany("NhanViens")
                        .HasForeignKey("ChucVuId");

                    b.HasOne("F4_API.Models.TaiKhoan", "TaiKhoan")
                        .WithMany("NhanViens")
                        .HasForeignKey("TaiKhoanId");

                    b.Navigation("ChucVu");

                    b.Navigation("TaiKhoan");
                });

            modelBuilder.Entity("F4_API.Models.NhapHang", b =>
                {
                    b.HasOne("F4_API.Models.NhanVien", "NhanVien")
                        .WithMany("NhapHangs")
                        .HasForeignKey("NhanVienId");

                    b.Navigation("NhanVien");
                });

            modelBuilder.Entity("F4_API.Models.Voucher", b =>
                {
                    b.HasOne("F4_API.Models.TaiKhoan", "TaiKhoan")
                        .WithMany("Vouchers")
                        .HasForeignKey("TaiKhoanId");

                    b.Navigation("TaiKhoan");
                });

            modelBuilder.Entity("F4_API.Models.ChucVu", b =>
                {
                    b.Navigation("NhanViens");
                });

            modelBuilder.Entity("F4_API.Models.Combo", b =>
                {
                    b.Navigation("ChiTiets");

                    b.Navigation("HoaDonCTs");
                });

            modelBuilder.Entity("F4_API.Models.DanhMuc", b =>
                {
                    b.Navigation("LinhKiens");

                    b.Navigation("ThuocTinhs");
                });

            modelBuilder.Entity("F4_API.Models.DanhMuc_LinhKien_ThuocTinh", b =>
                {
                    b.Navigation("LinhKienCTs");
                });

            modelBuilder.Entity("F4_API.Models.GiamGia", b =>
                {
                    b.Navigation("DotGiamGias");
                });

            modelBuilder.Entity("F4_API.Models.GioHang", b =>
                {
                    b.Navigation("ChiTiets");
                });

            modelBuilder.Entity("F4_API.Models.HinhThucThanhToan", b =>
                {
                    b.Navigation("HoaDons");
                });

            modelBuilder.Entity("F4_API.Models.HoaDon", b =>
                {
                    b.Navigation("ChiTiets");
                });

            modelBuilder.Entity("F4_API.Models.KhachHang", b =>
                {
                    b.Navigation("DiaChiKhachHangs");

                    b.Navigation("GioHangs");

                    b.Navigation("HoaDons");
                });

            modelBuilder.Entity("F4_API.Models.LinhKien", b =>
                {
                    b.Navigation("ChiTiets");

                    b.Navigation("DotGiamGias");

                    b.Navigation("GioHangCTs");
                });

            modelBuilder.Entity("F4_API.Models.LinhKienCT", b =>
                {
                    b.Navigation("HinhAnhs");
                });

            modelBuilder.Entity("F4_API.Models.NhanVien", b =>
                {
                    b.Navigation("NhapHangs");
                });

            modelBuilder.Entity("F4_API.Models.NhapHang", b =>
                {
                    b.Navigation("ChiTiets");
                });

            modelBuilder.Entity("F4_API.Models.TaiKhoan", b =>
                {
                    b.Navigation("HoaDons");

                    b.Navigation("KhachHangs");

                    b.Navigation("NhanViens");

                    b.Navigation("Vouchers");
                });

            modelBuilder.Entity("F4_API.Models.ThuongHieu", b =>
                {
                    b.Navigation("LinhKienCTs");
                });

            modelBuilder.Entity("F4_API.Models.Voucher", b =>
                {
                    b.Navigation("HoaDons");
                });
#pragma warning restore 612, 618
        }
    }
}
