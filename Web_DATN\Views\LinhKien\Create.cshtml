﻿@model Web_DATN.ViewModels.LinhKienFullViewModel
@using Microsoft.AspNetCore.Mvc.Rendering
@{
    ViewData["Title"] = "Thêm linh kiện";
}

<h2>Thêm mớ<PERSON></h2>

<form asp-action="Create" method="post">
    <div class="mb-3">
        <label asp-for="TenLinhKien" class="form-label"></label>
        <input asp-for="TenLinhKien" class="form-control" />
        <span asp-validation-for="TenLinhKien" class="text-danger"></span>
    </div>

    <div class="mb-3">
        <label asp-for="DanhMucId" class="form-label">Danh mục</label>
        <select asp-for="DanhMucId" asp-items="Model.DanhMucs" class="form-control" id="danhMucSelect">
            <option value="">-- <PERSON><PERSON><PERSON> danh mục --</option>
        </select>
        <span asp-validation-for="DanhMucId" class="text-danger"></span>
    </div>

    <div class="mb-3">
        <label asp-for="Gia" class="form-label"></label>
        <input asp-for="Gia" class="form-control" />
        <span asp-validation-for="Gia" class="text-danger"></span>
    </div>

    <div class="mb-3">
        <label asp-for="MoTa" class="form-label"></label>
        <textarea asp-for="MoTa" class="form-control"></textarea>
    </div>

    <div class="form-check mb-3">
        <input asp-for="TrangThai" class="form-check-input" />
        <label class="form-check-label" asp-for="TrangThai">Hiển thị</label>
    </div>

    <h4>Thuộc tính linh kiện</h4>
    <div id="thuocTinhList"></div>

    <button type="submit" class="btn btn-primary">Lưu</button>
</form>

@section Scripts {
    <script>
        document.getElementById("danhMucSelect").addEventListener("change", async function () {
            const danhMucId = this.value;
            const container = document.getElementById("thuocTinhList");
            container.innerHTML = "";

            if (!danhMucId) return;

            try {
                const response = await fetch(`https://localhost:7183/api/DanhMuc_LinhKien_ThuocTinh/thuoc-tinh/${danhMucId}`, { mode: 'cors' });
                if (!response.ok) throw new Error(response.status + ' ' + response.statusText);
                const data = await response.json();

                data.forEach((tt, index) => {
                    const html = `
                        <div class="border p-3 mb-2">
                            <input type="hidden" name="ThuocTinhs[\${index}].ThuocTinhId" value="\${tt.thuocTinhId}" />
                            <label class="form-label">\${tt.tenThuocTinh}</label>
                            <input name="ThuocTinhs[\${index}].GiaTri" class="form-control mb-2" placeholder="Nhập giá trị" />
                            <input name="ThuocTinhs[\${index}].MoTa" class="form-control mb-2" placeholder="Mô tả" />
                            <div class="form-check">
                                <input type="checkbox" name="ThuocTinhs[\${index}].TrangThai" class="form-check-input" checked />
                                <label class="form-check-label">Hiển thị</label>
                            </div>
                        </div>`;
                    container.insertAdjacentHTML('beforeend', html);
                });
            } catch (err) {
                console.error('Lỗi khi tải thuộc tính:', err);
            }
        });
    </script>
    @* Đảm bảo layout có @RenderSection("Scripts") *@
}
