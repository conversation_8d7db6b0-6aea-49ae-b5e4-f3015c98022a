﻿@inject F4_API.DATA.AppDbContext _dbContext
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - F4_Bán kiện điện tử</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/Web_DATN.styles.css" asp-append-version="true" />
    <script type="module" src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
</head>
<body>
    <header>
        @{
            string actionName = ViewContext.RouteData.Values["action"]?.ToString();
            string controllerName = ViewContext.RouteData.Values["controller"]?.ToString();
            var id = Context.Request.RouteValues["id"];
        }
        @if (!(controllerName == "TaiKhoan" && (actionName == "Login" || actionName == "DangKyNhanVien" || actionName == "LienHe")
        || (controllerName == "KhachHangs" && actionName == "Edit" && controllerName == "SanPham" && actionName == "QuanLySanPham" &&
        controllerName == "HoaDons" && actionName == "ThemHoaDonMoi" && id != null)))  // Thêm điều kiện hiển thị thanh tìm kiếm
        {
            <div class="search-bar">
                <form asp-action="Index" asp-controller="SanPham" method="get" class="d-flex">
                    <input class="form-control me-2 search-input" type="search" name="TimKiem" placeholder="Tìm kiếm..." autocomplete="off" aria-label="Search"><button class="btn btn-outline-success d-flex align-items-center justify-content-center" style="width: 130px; gap: 5px;" type="submit">
                        <ion-icon name="search"></ion-icon> Tìm Kiếm
                    </button>
                </form>
            </div>
        }
        @{
            var role = Context.Session.GetString("Role");
            var tenKhachHang = Context.Session.GetString("TenKhachHang");
            var tenNguoiDung = Context.Session.GetString("TenNguoiDung");
            var maKhachHang = Context.Session.GetString("MaKhachHang");
        }
        @if (role == "KhachHang")
        {
            <div class="account-icon-container">
                <div class="account-dropdown d-flex align-items-center">
                    <div class="account-dropdown">
                        <span class="ms-2">Xin chào, @tenKhachHang</span>
                    </div>
                    <div class="dropdown-menu">
                        <a href="@Url.Action("Edit", "KhachHangs", new { id = maKhachHang })">Thông tin cá nhân</a>
                        <a href="@Url.Action("Index", "HoaDon", new { id = maKhachHang })">Đơn mua</a>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="@Url.Action("Logout", "TaiKhoan")">Đăng xuất</a></li>
                    </div>
                </div>
            </div>
            <a href="@Url.Action("Index", "GioHang")" class="cart-icon">
                <i class="fas fa-shopping-cart"></i>
                <span class="cart-badge">@ViewBag.SoLuongGioHang</span>
            </a>
        }
        else if (role == "NhanVien" || role == "Admin")
        {
            <div class="account-icon-container">
                <span class="me-2">Xin chào, @tenNguoiDung (@role)</span>
                <a class="btn btn-outline-danger btn-sm" href="@Url.Action("Logout", "TaiKhoan")">Đăng xuất</a>
            </div>
        }
        else
        {
            <div class="account-login d-flex gap-2 account-icon-container">
                <a href="@Url.Action("Login", "TaiKhoan")">Đăng nhập</a> /
                <a href="@Url.Action("DangKy", "TaiKhoan")">Đăng ký</a>
            </div>
        }
        <button class="menu-toggle" id="menu-toggle" style="color : white ">
            <ion-icon id="menu-icon" name="menu-sharp"></ion-icon><!-- Biểu tượng mặc định -->
        </button>

        <div class="sidebar" id="sidebar">
            @{
                var danhMucList = _dbContext.DanhMucs.OrderBy(x => x.TenDanhMuc).ToList();
            }
            @if (role == "Admin")
            {
                <ul class="nav flex-column">
                    <nav>
                        <a class="navbar-brand">
                            <img src="~/img/Logo.png" alt="Poly Food" class="nav-logo" />
                        </a>
                    </nav>
                    <li class="nav-item p-3">
                        <a class="nav-link" href="@Url.Action("ThongKe", "HoaDon")">
                            <i class="fas fa-chart-line p-2"></i> Thống kê Đơn hàng
                        </a>
                    </li>
                    <li class="nav-item p-3">
                        <a class="nav-link" href="@Url.Action("QuanLySanPham", "SanPham")">
                            <i class="fas fa-cogs p-2"></i> Quản lý Sản phẩm
                        </a>
                    </li>
                    <li class="nav-item p-3">
                        <a class="nav-link" href="@Url.Action("XacNhanDon", "HoaDon")">
                            <i class="fas fa-check-circle p-2"></i> Xác nhận Đơn hàng
                        </a>
                    </li>
                    <li class="nav-item p-3">
                        <a class="nav-link" href="@Url.Action("HoaDon", "HoaDon")">
                            <i class="fas fa-file-invoice p-2"></i> Quản lý Hóa đơn
                        </a>
                    </li>
                    <li class="nav-item p-3">
                        <a class="nav-link" href="@Url.Action("DanhSachKhachHang", "KhachHangs")">
                            <i class="fas fa-users p-2"></i> Quản lý Khách hàng
                        </a>
                    </li>
                    <li class="nav-item p-3">
                        <a class="nav-link" href="@Url.Action("DangKyNhanVien", "NhanViens")">
                            <i class="fas fa-file-invoice p-2"></i> Thanh Toán Trực Tiếp
                        </a>
                    </li> 
                </ul>
            }
            else if (role == "NhanVien")
            {
                <ul class="nav flex-column">
                    <nav>
                        <a class="navbar-brand">
                            <img src="~/img/Logo.png" alt="Poly Food" class="nav-logo" />
                        </a>
                    </nav>
                    <li class="nav-item p-3">
                        <a class="nav-link" href="@Url.Action("ThemHoaDonMoi", "HoaDon")">
                            <i class="fas fa-cogs p-2"></i> Bán hàng tại quầy
                        </a>
                    </li>
                    <li class="nav-item p-3">
                        <a class="nav-link" href="@Url.Action("HoaDon", "Trahang")">
                            <i class="fas fa-users p-2"></i> Trả hàng
                        </a>
                    </li>
                </ul>
            }

            else if (role == "KhachHang")
            {
                <ul class="nav flex-column">
                    <nav>
                        <a class="navbar-brand" asp-controller="Home" asp-action="Index">
                            <img src="~/img/Logo.png" alt="Poly Food" class="nav-logo" />
                        </a>
                    </nav>

                    @if (danhMucList != null)
                    {
                        foreach (var dm in danhMucList)
                        {
                            <li class="nav-item p-2">
                                <a class="nav-link" asp-controller="SanPham" asp-action="Index" asp-route-loaiSanPham="@dm.DanhMucId">
                                    <ion-icon name="albums-outline"></ion-icon> @dm.TenDanhMuc
                                </a>
                            </li>
                        }
                    }

                    <li class="nav-item p-2">
                        <a class="nav-link" asp-controller="DangNhap" asp-action="LienHe">
                            <ion-icon name="add-circle-sharp"></ion-icon> Liên Hệ
                        </a>
                    </li>
                </ul>
            }

            else
            {
                <ul class="nav flex-column">
                    <nav>
                        <a class="navbar-brand" asp-controller="Home" asp-action="Index">
                            <img src="~/img/Logo.png" alt="Poly Food" class="nav-logo" />
                        </a>
                    </nav>

                    @if (ViewBag.DanhMucList != null)
                    {
                        foreach (var dm in ViewBag.DanhMucList)
                        {
                            <li class="nav-item p-2">
                                <a class="nav-link" asp-controller="SanPham" asp-action="Index" asp-route-loaiSanPham="@dm.DanhMucId">
                                    <ion-icon name="albums-outline"></ion-icon> @dm.TenDanhMuc
                                </a>
                            </li>
                        }
                    }

                    <li class="nav-item p-2">
                        <a class="nav-link" asp-controller="DangNhap" asp-action="LienHe">
                            <ion-icon name="add-circle-sharp"></ion-icon> Liên Hệ
                        </a>
                    </li>
                </ul>
            }


        </div>
    </header>
    <div class="main-content">
        <main role="main">
            @RenderBody()
        </main>
    </div>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const sidebar = document.getElementById('sidebar');
            const menuToggle = document.getElementById('menu-toggle');
            const menuIcon = document.getElementById('menu-icon');
            const mainContent = document.querySelector('.main-content');

            if (localStorage.getItem('sidebarActive') === 'true') {
                sidebar.classList.add('active');
                mainContent.style.marginLeft = '250px';
                menuIcon.style.color = 'white';
            } else {
                menuIcon.style.color = 'black';
            }

            menuToggle.addEventListener('click', function () {
                sidebar.classList.toggle('active');

                if (sidebar.classList.contains('active')) {
                    mainContent.style.marginLeft = '250px';
                    menuIcon.style.color = 'white';
                    localStorage.setItem('sidebarActive', 'true');
                } else {
                    mainContent.style.marginLeft = '0';
                    menuIcon.style.color = 'black';
                    localStorage.setItem('sidebarActive', 'false');
                }
            });
        });
    </script>
</body>
</html>
